# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
dist-ssr
build
out
.next
.nuxt
.vuepress/dist
.serverless
.fusebox
coverage
storybook-static

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
/coverage
.nyc_output

# Misc
.DS_Store
Thumbs.db
.cache
.project
.settings
.tmproj
*.esproj
nbproject
.directory
._*

// src/api/services.js
import apiClient from "./client";

// Document APIs
export const uploadDocument = (file, extractEntities = true) => {
  const formData = new FormData();
  formData.append("file", file);
  return apiClient.post(
    `/documents/upload?extract_entities=${extractEntities}`,
    formData,
    {
      headers: { "Content-Type": "multipart/form-data" },
    }
  );
};

export const getDocuments = () => apiClient.get("/documents/");
// Remove console.log to avoid unnecessary API calls
export const getDocument = (documentId) =>
  apiClient.get(`/documents/${documentId}`);
export const deleteDocument = (documentId) =>
  apiClient.delete(`/documents/${documentId}`);

// Graph APIs
export const getKnowledgeGraph = (documentId) =>
  apiClient.get(`/graph/${documentId}`);
export const filterKnowledgeGraph = (documentId, keywords) =>
  apiClient.post(`/graph/${documentId}/filter`, { keywords });
export const executeCypherQuery = (query) =>
  apiClient.post("/graph/query", { query });
export const generateCypherQuery = (documentId, question) =>
  apiClient.post("/graph/generate-query", {
    document_id: documentId,
    question,
  });

// Chat APIs
export const askQuestion = (documentId, question) =>
  apiClient.post("/chat/ask", { document_id: documentId, question });
export const getChatHistory = (documentId) =>
  apiClient.get(`/chat/history/${documentId}`);

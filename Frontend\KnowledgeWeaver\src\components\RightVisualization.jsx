import { Send, ChevronLeft, ChevronRight, X, <PERSON>u } from "lucide-react";
const RightVisualization = ({ rightPanelVisible, toggleRightPanel }) => {
  return (
    <>
      {rightPanelVisible && (
        <div className="w-96 border-l border-gray-800 flex flex-col relative">
          {/* Close button */}
          <button
            className="absolute right-2 top-2 p-1 rounded hover:bg-gray-700"
            onClick={toggleRightPanel}
          >
            <X size={16} />
          </button>

          <div className="p-4 border-b border-gray-800 flex justify-between items-center">
            <h2 className="text-sm uppercase font-bold text-gray-400">
              Document Preview
            </h2>
          </div>

          <div className="p-4">
            {/* Document Metadata */}
            <div>
              <h3 className="text-sm uppercase font-bold text-gray-400 mb-2">
                Document Metadata
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">File Name:</span>
                  <span className="text-sm">financial_report.pdf</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">File Size:</span>
                  <span className="text-sm">2.4 MB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Uploaded:</span>
                  <span className="text-sm">April 23, 2025</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Entities:</span>
                  <span className="text-sm">42</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-400">Relationships:</span>
                  <span className="text-sm">65</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
export default RightVisualization;

import { useState, useRef, useEffect } from "react";
import { Send, ChevronLeft, ChevronRight, X, Menu } from "lucide-react";
import MiddleChatSection from "./components/MiddleChatSection";
import GraphVisualization from "./components/GraphVisualization";
import LeftSidebar from "./components/LeftSidebar";
import RightVisualization from "./components/RightVisualization";
import Header from "./components/Header";
import Debug from "./components/Debug";
import { getDocuments } from "./api/services";

function GraphRAG() {
  const [activeTab, setActiveTab] = useState("Knowledge Graph Analysis");
  const [documents, setDocuments] = useState([]);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [loading, setLoading] = useState(false);

  const [chatHistory, setChatHistory] = useState([
    {
      role: "assistant",
      content:
        "Hello! I'm your GraphRAG assistant. You can upload documents and images, and I'll help you analyze them using knowledge graphs.",
    },
    {
      role: "user",
      content:
        "I've uploaded a financial report. Can you extract the key entities and relationships?",
    },
    {
      role: "assistant",
      content:
        "I've analyzed the financial report and extracted entities like companies, executives, and financial metrics. I've also identified relationships between these entities. You can see the knowledge graph visualization on the right.",
    },
    {
      role: "user",
      content: "Great! Can you tell me more about the relationship between",
    },
  ]);

  const [inputMessage, setInputMessage] = useState("");
  const messageEndRef = useRef(null);
  const [leftSidebarVisible, setLeftSidebarVisible] = useState(true);
  const [rightPanelVisible, setRightPanelVisible] = useState(true);

  // Fetch documents on component mount
  useEffect(() => {
    fetchDocuments();
  }, []);

  // Fetch documents from API
  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const response = await getDocuments();
      console.log("Documents from API:", response.data);

      // Check if response.data is an object with documents property or an array
      if (response.data && response.data.documents) {
        setDocuments(response.data.documents);
      } else if (Array.isArray(response.data)) {
        setDocuments(response.data);
      } else {
        console.error("Unexpected API response format:", response.data);
        setDocuments([]);
      }
    } catch (err) {
      console.error("Error fetching documents:", err);
      // Set documents to empty array to prevent map() error
      setDocuments([]);

      // Add error message to chat history
      setChatHistory((prev) => [
        ...prev,
        {
          role: "assistant",
          content:
            "I'm having trouble connecting to the backend database. Please make sure the backend server is running correctly and the Neo4j database is accessible.",
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [chatHistory]);

  // Handle sending a new message
  const handleSendMessage = () => {
    if (inputMessage.trim() === "") return;

    // Add user message to chat
    setChatHistory([...chatHistory, { role: "user", content: inputMessage }]);
    setInputMessage("");

    // Simulate assistant response (in a real app, this would be an API call)
    setTimeout(() => {
      setChatHistory((prev) => [
        ...prev,
        {
          role: "assistant",
          content: `I've processed your question about "${inputMessage}". What specific information would you like to know?`,
        },
      ]);
    }, 1000);
  };

  // Toggle sidebars
  const toggleLeftSidebar = () => setLeftSidebarVisible(!leftSidebarVisible);
  const toggleRightPanel = () => setRightPanelVisible(!rightPanelVisible);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Sidebar Toggle Button (when collapsed) */}
        {!leftSidebarVisible && (
          <button
            className="absolute left-0 top-1/2 z-10 p-2 bg-gray-800 rounded-r-lg hover:bg-gray-700"
            onClick={toggleLeftSidebar}
          >
            <ChevronRight size={20} />
          </button>
        )}

        {/* Left Sidebar */}
        <LeftSidebar
          leftSidebarVisible={leftSidebarVisible}
          toggleLeftSidebar={toggleLeftSidebar}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          documents={documents}
          selectedDocument={selectedDocument}
          setSelectedDocument={setSelectedDocument}
          fetchDocuments={fetchDocuments}
          setChatHistory={setChatHistory}
        />

        {/* Middle Chat Section */}
        <MiddleChatSection
          leftSidebarVisible={leftSidebarVisible}
          chatHistory={chatHistory}
          messageEndRef={messageEndRef}
          setInputMessage={setInputMessage}
          handleSendMessage={handleSendMessage}
          inputMessage={inputMessage}
          toggleLeftSidebar={toggleLeftSidebar}
        />
        {/* Graph Visualization Section */}
        <GraphVisualization
          toggleLeftSidebar={toggleLeftSidebar}
          toggleRightPanel={toggleRightPanel}
          rightPanelVisible={rightPanelVisible}
          selectedDocument={selectedDocument}
        />

        {/* Right Visualization Panel (with collapse functionality) */}
        <RightVisualization
          toggleRightPanel={toggleRightPanel}
          rightPanelVisible={rightPanelVisible}
        />

        {/* Debug Component */}
        <Debug documents={documents} />
      </div>
    </div>
  );
}

export default GraphRAG;

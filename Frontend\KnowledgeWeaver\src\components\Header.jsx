import { Send, ChevronLeft, ChevronRight, X, <PERSON>u } from "lucide-react";
const Header = ({ toggleLeftSidebar }) => {
  return (
    <>
      <header className="flex items-center p-4 border-b border-gray-800">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded bg-purple-600 flex items-center justify-center text-xl mr-2">
            K
          </div>
          <h1 className="text-xl font-bold text-purple-400">KnowledgeWeaver</h1>
        </div>
        <div className="ml-auto">
          <button className="px-4 py-2 bg-gray-700 rounded text-sm">
            Settings
          </button>
          <div className="ml-2 inline-block w-8 h-8 rounded-full bg-blue-500 text-center">
            U
          </div>
        </div>
      </header>
    </>
  );
};
export default Header;
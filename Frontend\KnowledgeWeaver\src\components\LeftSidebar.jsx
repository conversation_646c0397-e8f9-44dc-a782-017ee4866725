// import { Send, ChevronLeft, ChevronRight, <PERSON>, Menu } from "lucide-react";
// import { useState } from "react";
// const LeftSidebar = ({
//   leftSidebarVisible,
//   toggleLeftSidebar,
//   documents,
//   selectedDocument,
//   setSelectedDocument,
//   activeTab,
// }) => {
//   const [uploadedFiles, setUploadedFiles] = useState([
//     { name: "financial_report.pdf", type: "pdf" },
//     { name: "diagram.png", type: "image" },
//     { name: "research_paper.pdf", type: "pdf" },
//   ]);
//   const handleFileUpload = (e) => {
//     const file = e.target.files[0];
//     if (!file) return;

//     // Add file to uploaded files
//     setUploadedFiles([
//       ...uploadedFiles,
//       {
//         name: file.name,
//         type: file.type.includes("image") ? "image" : "pdf",
//       },
//     ]);

//     // Simulate processing message
//     setChatHistory([
//       ...chatHistory,
//       {
//         role: "assistant",
//         content: `I've received your file "${file.name}". Would you like me to analyze it?`,
//       },
//     ]);
//   };
//   return (
//     <>
//       {leftSidebarVisible && (
//         <div className="w-64 border-r border-gray-800 flex flex-col relative">
//           {/* Collapse button */}
//           <button
//             className="absolute right-2 top-2 p-1 rounded hover:bg-gray-700"
//             onClick={toggleLeftSidebar}
//           >
//             <ChevronLeft size={16} />
//           </button>

//           <div className="p-4 border-b border-gray-800">
//             <h2 className="text-sm uppercase font-bold text-gray-400 mb-2">
//               Chat History
//             </h2>
//             <div
//               className={`p-3 rounded-lg mb-2 cursor-pointer ${
//                 activeTab === "Knowledge Graph Analysis"
//                   ? "bg-purple-900"
//                   : "hover:bg-gray-800"
//               }`}
//               onClick={() => setActiveTab("Knowledge Graph Analysis")}
//             >
//               <div className="flex items-center">
//                 <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center mr-2">
//                   <svg
//                     className="w-4 h-4"
//                     viewBox="0 0 24 24"
//                     fill="none"
//                     stroke="currentColor"
//                     strokeWidth="2"
//                   >
//                     <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" />
//                   </svg>
//                 </div>
//                 <span className="text-sm">Knowledge Graph Analysis</span>
//               </div>
//             </div>

//             <div
//               className="p-3 rounded-lg mb-2 hover:bg-gray-800 cursor-pointer"
//               onClick={() => setActiveTab("Document Summarization")}
//             >
//               <div className="flex items-center">
//                 <div className="w-6 h-6 rounded bg-gray-700 flex items-center justify-center mr-2">
//                   <svg
//                     className="w-4 h-4"
//                     viewBox="0 0 24 24"
//                     fill="none"
//                     stroke="currentColor"
//                     strokeWidth="2"
//                   >
//                     <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z" />
//                   </svg>
//                 </div>
//                 <span className="text-sm">Document Summarization</span>
//               </div>
//             </div>

//             <div
//               className="p-3 rounded-lg mb-2 hover:bg-gray-800 cursor-pointer"
//               onClick={() => setActiveTab("Entity Recognition")}
//             >
//               <div className="flex items-center">
//                 <div className="w-6 h-6 rounded bg-blue-500 flex items-center justify-center mr-2">
//                   <svg
//                     className="w-4 h-4"
//                     viewBox="0 0 24 24"
//                     fill="none"
//                     stroke="currentColor"
//                     strokeWidth="2"
//                   >
//                     <circle cx="12" cy="12" r="10" />
//                     <circle cx="12" cy="12" r="4" />
//                   </svg>
//                 </div>
//                 <span className="text-sm">Entity Recognition</span>
//               </div>
//             </div>

//             <div
//               className="p-3 rounded-lg mb-2 hover:bg-gray-800 cursor-pointer"
//               onClick={() => setActiveTab("Relationship Extraction")}
//             >
//               <div className="flex items-center">
//                 <div className="w-6 h-6 rounded bg-gray-700 flex items-center justify-center mr-2">
//                   <svg
//                     className="w-4 h-4"
//                     viewBox="0 0 24 24"
//                     fill="none"
//                     stroke="currentColor"
//                     strokeWidth="2"
//                   >
//                     <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6" />
//                     <path d="M15 3h6v6" />
//                     <path d="M10 14L21 3" />
//                   </svg>
//                 </div>
//                 <span className="text-sm">Relationship Extraction</span>
//               </div>
//             </div>
//           </div>

//           <div className="p-4 border-b border-gray-800 flex-1">
//             <h2 className="text-sm uppercase font-bold text-gray-400 mb-2">
//               Documents & Images
//             </h2>
//             <label className="block w-full py-2 bg-purple-600 rounded-lg text-white text-sm mb-4 text-center cursor-pointer">
//               + Upload File
//               <input
//                 type="file"
//                 className="hidden"
//                 onChange={handleFileUpload}
//               />
//             </label>

//             {uploadedFiles.map((file, index) => (
//               <div
//                 key={index}
//                 className="p-3 bg-gray-800 rounded-lg mb-2 flex items-center"
//               >
//                 <div className="w-6 h-6 rounded bg-gray-700 flex items-center justify-center mr-2">
//                   {file.type === "pdf" ? (
//                     <svg
//                       className="w-4 h-4"
//                       viewBox="0 0 24 24"
//                       fill="none"
//                       stroke="currentColor"
//                       strokeWidth="2"
//                     >
//                       <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z" />
//                     </svg>
//                   ) : (
//                     <svg
//                       className="w-4 h-4"
//                       viewBox="0 0 24 24"
//                       fill="none"
//                       stroke="currentColor"
//                       strokeWidth="2"
//                     >
//                       <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
//                       <circle cx="8.5" cy="8.5" r="1.5" />
//                       <path d="M21 15l-5-5L5 21" />
//                     </svg>
//                   )}
//                 </div>
//                 <span className="text-sm">{file.name}</span>
//               </div>
//             ))}
//           </div>

//           <div className="p-2 text-center text-xs text-gray-500">
//             Made with by KnowledgeWeaver
//           </div>
//         </div>
//       )}
//     </>
//   );
// };
// export default LeftSidebar;

// src/components/LeftSidebar.jsx
import { ChevronLeft } from "lucide-react";
import { useState } from "react";
import { uploadDocument } from "../api/services";

const LeftSidebar = ({
  leftSidebarVisible,
  toggleLeftSidebar,
  documents,
  selectedDocument,
  setSelectedDocument,
  activeTab,
  setActiveTab,
  fetchDocuments,
  setChatHistory,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [extractEntities, setExtractEntities] = useState(true);

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      setLoading(true);
      await uploadDocument(file, extractEntities);

      // Refresh document list
      fetchDocuments();

      // Add message to chat
      setChatHistory((prev) => [
        ...prev,
        {
          role: "assistant",
          content: `I've received your file "${file.name}" and processed it. You can now ask questions about it or explore the knowledge graph.`,
        },
      ]);

      setError(null);
    } catch (err) {
      console.error("Error uploading document:", err);
      setError("Failed to upload document");

      // Add error message to chat
      setChatHistory((prev) => [
        ...prev,
        {
          role: "assistant",
          content: `I encountered an error while processing "${file.name}". Please try again.`,
        },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Debug: Log documents prop
  console.log("LeftSidebar documents:", documents);

  return (
    <>
      {leftSidebarVisible && (
        <div className="w-64 border-r border-gray-800 flex flex-col relative">
          {/* Collapse button */}
          <button
            className="absolute right-2 top-2 p-1 rounded hover:bg-gray-700"
            onClick={toggleLeftSidebar}
          >
            <ChevronLeft size={16} />
          </button>

          <div className="p-4 border-b border-gray-800">
            <h2 className="text-sm uppercase font-bold text-gray-400 mb-2">
              Chat History
            </h2>
            <div
              className={`p-3 rounded-lg mb-2 cursor-pointer ${
                activeTab === "Knowledge Graph Analysis"
                  ? "bg-purple-900"
                  : "hover:bg-gray-800"
              }`}
              onClick={() => setActiveTab("Knowledge Graph Analysis")}
            >
              <div className="flex items-center">
                <div className="w-6 h-6 rounded-full bg-purple-600 flex items-center justify-center mr-2">
                  <svg
                    className="w-4 h-4"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M21 15a2 2 0 01-2 2H7l-4 4V5a2 2 0 012-2h14a2 2 0 012 2z" />
                  </svg>
                </div>
                <span className="text-sm">Knowledge Graph Analysis</span>
              </div>
            </div>

            {/* Other tabs... */}
          </div>

          <div className="p-4 border-b border-gray-800 flex-1">
            <h2 className="text-sm uppercase font-bold text-gray-400 mb-2">
              Documents & Images
            </h2>

            <div className="mb-2">
              <label className="flex items-center text-sm text-gray-400">
                <input
                  type="checkbox"
                  checked={extractEntities}
                  onChange={(e) => setExtractEntities(e.target.checked)}
                  className="mr-2"
                />
                Extract entities
              </label>
              <p className="text-xs text-gray-500 mt-1">
                {extractEntities
                  ? "Uses OpenAI to extract entities (uses API credits)"
                  : "No OpenAI API calls will be made"}
              </p>
            </div>

            <label className="block w-full py-2 bg-purple-600 rounded-lg text-white text-sm mb-4 text-center cursor-pointer">
              {loading ? "Uploading..." : "+ Upload File"}
              <input
                type="file"
                className="hidden"
                onChange={handleFileUpload}
                disabled={loading}
              />
            </label>

            {error && <div className="text-red-500 text-sm mb-2">{error}</div>}

            {documents && documents.length > 0 ? (
              documents.map((doc) => (
                <div
                  key={doc.document_id}
                  className={`p-3 rounded-lg mb-2 cursor-pointer ${
                    selectedDocument?.document_id === doc.document_id
                      ? "bg-purple-900"
                      : "bg-gray-800 hover:bg-gray-700"
                  }`}
                  onClick={() => setSelectedDocument(doc)}
                >
                  <div className="flex items-start">
                    <div className="w-6 h-6 rounded bg-gray-700 flex items-center justify-center mr-2 mt-1">
                      <svg
                        className="w-4 h-4"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z" />
                      </svg>
                    </div>
                    <div className="flex flex-col flex-1 overflow-hidden">
                      <span className="text-sm font-medium truncate">
                        {doc.metadata?.filename ||
                          `Document ${doc.document_id.substring(0, 8)}...`}
                      </span>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <span className="mr-2">
                          ID: {doc.document_id.substring(0, 8)}...
                        </span>
                        {doc.metadata?.chunk_count > 0 && (
                          <span className="mr-2">
                            {doc.metadata.chunk_count} chunks
                          </span>
                        )}
                        {doc.metadata?.type &&
                          doc.metadata.type !== "Unknown" && (
                            <span>{doc.metadata.type}</span>
                          )}
                      </div>
                      {doc.text_preview && (
                        <p className="text-xs text-gray-400 mt-1 line-clamp-2 overflow-hidden">
                          {doc.text_preview}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-400">No documents found</p>
                <p className="text-gray-500 text-xs mt-1">
                  Upload a document to get started
                </p>
              </div>
            )}
          </div>

          <div className="p-2 text-center text-xs text-gray-500">
            Made with by KnowledgeWeaver
          </div>
        </div>
      )}
    </>
  );
};
export default LeftSidebar;
